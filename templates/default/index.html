{{template "layout.html" .}}

{{define "title"}}{{.page_title}} - 视频网站{{end}}

{{define "content"}}
{{range .type_sections}}
<section class="type-section">
    <div class="section-header">
        <h2>{{.TypeName}}</h2>
        <a href="/type/{{.TypeID}}" class="more-link">更多 &raquo;</a>
    </div>
    
    <div class="section-content">
        <!-- 热门视频 -->
        {{if .hot_videos}}
        <div class="video-list">
            <h3>热门推荐</h3>
            <div class="video-grid">
                {{range .hot_videos}}
                <div class="video-item">
                    <div class="video-thumb">
                        <a href="/detail/{{.VodID}}">
                            {{if .VodPic}}
                            <img src="{{fixImagePath .VodPic}}" alt="{{.VodName}}" loading="lazy">
                            {{else}}
                            <div class="no-image">暂无图片</div>
                            {{end}}
                        </a>
                    </div>
                    <div class="video-info">
                        <h4><a href="/detail/{{.VodID}}">{{.VodName}}</a></h4>
                        <p class="video-meta">
                            {{if .VodRemarks}}<span class="remarks">{{.VodRemarks}}</span>{{end}}
                            {{if .VodYear}}<span class="year">{{.VodYear}}</span>{{end}}
                        </p>
                    </div>
                </div>
                {{end}}
            </div>
        </div>
        {{end}}

        <!-- 最新视频 -->
        {{if .latest_videos}}
        <div class="video-list">
            <h3>最新更新</h3>
            <div class="video-grid">
                {{range .latest_videos}}
                <div class="video-item">
                    <div class="video-thumb">
                        <a href="/detail/{{.VodID}}">
                            {{if .VodPic}}
                            <img src="{{fixImagePath .VodPic}}" alt="{{.VodName}}" loading="lazy">
                            {{else}}
                            <div class="no-image">暂无图片</div>
                            {{end}}
                        </a>
                    </div>
                    <div class="video-info">
                        <h4><a href="/detail/{{.VodID}}">{{.VodName}}</a></h4>
                        <p class="video-meta">
                            {{if .VodRemarks}}<span class="remarks">{{.VodRemarks}}</span>{{end}}
                            {{if .VodYear}}<span class="year">{{.VodYear}}</span>{{end}}
                        </p>
                    </div>
                </div>
                {{end}}
            </div>
        </div>
        {{end}}

        <!-- 随机视频 -->
        {{if .random_videos}}
        <div class="video-list">
            <h3>随机推荐</h3>
            <div class="video-grid">
                {{range .random_videos}}
                <div class="video-item">
                    <div class="video-thumb">
                        <a href="/detail/{{.VodID}}">
                            {{if .VodPic}}
                            <img src="{{fixImagePath .VodPic}}" alt="{{.VodName}}" loading="lazy">
                            {{else}}
                            <div class="no-image">暂无图片</div>
                            {{end}}
                        </a>
                    </div>
                    <div class="video-info">
                        <h4><a href="/detail/{{.VodID}}">{{.VodName}}</a></h4>
                        <p class="video-meta">
                            {{if .VodRemarks}}<span class="remarks">{{.VodRemarks}}</span>{{end}}
                            {{if .VodYear}}<span class="year">{{.VodYear}}</span>{{end}}
                        </p>
                    </div>
                </div>
                {{end}}
            </div>
        </div>
        {{end}}
    </div>
</section>
{{end}}

{{if not .type_sections}}
<div class="empty-content">
    <h2>暂无内容</h2>
    <p>网站正在建设中，敬请期待...</p>
</div>
{{end}}
{{end}}
