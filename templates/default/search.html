{{template "layout.html" .}}

{{define "title"}}搜索结果 - 视频网站{{end}}

{{define "breadcrumb"}}
<div class="breadcrumb">
    <a href="/">首页</a> &gt; 
    <span>搜索结果</span>
    {{if .keyword}}
    &gt; <span>"{{.keyword}}"</span>
    {{end}}
</div>
{{end}}

{{define "content"}}
<div class="search-page">
    <div class="search-header">
        <h1>搜索结果</h1>
        {{if .keyword}}
        <p class="search-keyword">关键词: "{{.keyword}}"</p>
        {{end}}
        
        <!-- 搜索框 -->
        <div class="search-form">
            <form action="/search" method="get">
                <input type="text" name="keyword" placeholder="搜索视频..." value="{{.keyword}}" required>
                <button type="submit">搜索</button>
            </form>
        </div>
    </div>

    {{if .videos}}
    <div class="search-results">
        <div class="result-count">
            <p>找到 {{.pagination.total}} 个相关视频</p>
        </div>

        <div class="video-grid">
            {{range .videos}}
            <div class="video-item">
                <div class="video-thumb">
                    <a href="/detail/{{.VodID}}">
                        {{if .VodPic}}
                        <img src="{{fixImagePath .VodPic}}" alt="{{.VodName}}" loading="lazy">
                        {{else}}
                        <div class="no-image">暂无图片</div>
                        {{end}}
                    </a>
                </div>
                <div class="video-info">
                    <h4><a href="/detail/{{.VodID}}">{{.VodName}}</a></h4>
                    <p class="video-meta">
                        {{if .VodRemarks}}<span class="remarks">{{.VodRemarks}}</span>{{end}}
                        {{if .VodYear}}<span class="year">{{.VodYear}}</span>{{end}}
                        {{if .VodArea}}<span class="area">{{.VodArea}}</span>{{end}}
                    </p>
                    {{if .VodContent}}
                    <p class="video-desc">{{.VodContent}}</p>
                    {{end}}
                </div>
            </div>
            {{end}}
        </div>

        <!-- 分页 -->
        {{if gt .pagination.totalPages 1}}
        <div class="pagination">
            {{if gt .pagination.current 1}}
            <a href="/search?keyword={{.keyword}}&page=1">&laquo; 首页</a>
            <a href="/search?keyword={{.keyword}}&page={{subtract .pagination.current 1}}">&lsaquo; 上一页</a>
            {{end}}

            {{$current := .pagination.current}}
            {{$total := .pagination.totalPages}}
            {{$start := 1}}
            {{$end := $total}}
            
            {{if gt $total 10}}
                {{if gt $current 5}}
                    {{$start = subtract $current 4}}
                {{end}}
                {{if lt (add $current 5) $total}}
                    {{$end = add $current 5}}
                {{else}}
                    {{$end = $total}}
                    {{if gt $total 10}}
                        {{$start = subtract $total 9}}
                    {{end}}
                {{end}}
            {{end}}

            {{range $i := $start}}
                {{if le $i $end}}
                    {{if eq $i $current}}
                    <span class="current">{{$i}}</span>
                    {{else}}
                    <a href="/search?keyword={{$.keyword}}&page={{$i}}">{{$i}}</a>
                    {{end}}
                {{end}}
            {{end}}

            {{if lt .pagination.current .pagination.totalPages}}
            <a href="/search?keyword={{.keyword}}&page={{add .pagination.current 1}}">下一页 &rsaquo;</a>
            <a href="/search?keyword={{.keyword}}&page={{.pagination.totalPages}}">末页 &raquo;</a>
            {{end}}
        </div>

        <div class="pagination-info">
            <p>共 {{.pagination.total}} 个视频，当前第 {{.pagination.current}}/{{.pagination.totalPages}} 页</p>
        </div>
        {{end}}
    </div>

    {{else if .keyword}}
    <div class="empty-content">
        <h2>没有找到相关视频</h2>
        <p>抱歉，没有找到与 "{{.keyword}}" 相关的视频</p>
        <div class="search-suggestions">
            <h3>搜索建议:</h3>
            <ul>
                <li>检查关键词是否正确</li>
                <li>尝试使用更简单的关键词</li>
                <li>尝试使用同义词或相关词汇</li>
            </ul>
        </div>
        <a href="/" class="back-home">返回首页</a>
    </div>

    {{else}}
    <div class="empty-content">
        <h2>请输入搜索关键词</h2>
        <p>在上方搜索框中输入您要查找的视频名称</p>
    </div>
    {{end}}
</div>
{{end}}

{{define "head"}}
<style>
.search-page {
    max-width: 1200px;
    margin: 0 auto;
}

.search-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.search-keyword {
    color: #666;
    margin-bottom: 20px;
    font-size: 16px;
}

.search-form {
    max-width: 500px;
    margin: 0 auto;
}

.search-form form {
    display: flex;
    gap: 10px;
}

.search-form input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.search-form input:focus {
    outline: none;
    border-color: #3498db;
}

.search-form button {
    padding: 12px 24px;
    background: #3498db;
    color: #fff;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s;
}

.search-form button:hover {
    background: #2980b9;
}

.search-results {
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.result-count {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.result-count p {
    color: #666;
    font-size: 14px;
}

.video-desc {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.pagination-info {
    text-align: center;
    margin-top: 20px;
    color: #666;
    font-size: 14px;
}

.empty-content {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.empty-content h2 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.empty-content p {
    color: #666;
    margin-bottom: 20px;
}

.search-suggestions {
    max-width: 400px;
    margin: 30px auto;
    text-align: left;
}

.search-suggestions h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
}

.search-suggestions ul {
    list-style: none;
    padding: 0;
}

.search-suggestions li {
    padding: 5px 0;
    color: #666;
    position: relative;
    padding-left: 20px;
}

.search-suggestions li:before {
    content: "•";
    color: #3498db;
    position: absolute;
    left: 0;
}

.back-home {
    display: inline-block;
    margin-top: 20px;
    padding: 10px 20px;
    background: #3498db;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    transition: background 0.3s;
}

.back-home:hover {
    background: #2980b9;
}

.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f5f5f5;
    color: #999;
    font-size: 12px;
}

@media (max-width: 768px) {
    .search-form form {
        flex-direction: column;
    }
    
    .search-form button {
        margin-top: 10px;
    }
}
</style>
{{end}}
