{{template "layout.html" .}}

{{define "title"}}{{.page_title}} - 视频网站{{end}}

{{define "breadcrumb"}}
<div class="breadcrumb">
    <a href="/">首页</a> > <span>搜索结果</span>
</div>
{{end}}

{{define "content"}}
{{if .keyword}}
<h1>搜索结果：{{.keyword}}</h1>

{{if .videos}}
<div class="search-results">
    <p class="result-count">找到 {{.pagination.total}} 个相关视频</p>
    
    <div class="video-grid">
        {{range .videos}}
        <div class="video-item">
            <div class="video-thumb">
                <a href="/detail/{{.VodID}}">
                    {{if .VodPic}}
                    <img src="{{fixImagePath .VodPic}}" alt="{{.VodName}}" loading="lazy">
                    {{else}}
                    <img src="/assets/images/no-image.jpg" alt="{{.VodName}}" loading="lazy">
                    {{end}}
                </a>
            </div>
            <div class="video-info">
                <h3><a href="/detail/{{.VodID}}">{{.VodName}}</a></h3>
                <p class="video-meta">
                    {{if .VodRemarks}}<span class="remarks">{{.VodRemarks}}</span>{{end}}
                    {{if .VodYear}}<span class="year">{{.VodYear}}</span>{{end}}
                </p>
            </div>
        </div>
        {{end}}
    </div>

    <!-- 分页 -->
    {{if gt .pagination.totalPages 1}}
    <div class="pagination">
        {{if gt .pagination.current 1}}
        <a href="/search?keyword={{.keyword}}&page={{subtract .pagination.current 1}}" class="prev">上一页</a>
        {{end}}
        
        {{$current := .pagination.current}}
        {{$total := .pagination.totalPages}}
        {{$keyword := .keyword}}
        
        {{range $i := 1}}
        {{if le $i $total}}
            {{if eq $i $current}}
            <span class="current">{{$i}}</span>
            {{else}}
            <a href="/search?keyword={{$keyword}}&page={{$i}}">{{$i}}</a>
            {{end}}
        {{end}}
        {{end}}
        
        {{if lt .pagination.current .pagination.totalPages}}
        <a href="/search?keyword={{.keyword}}&page={{add .pagination.current 1}}" class="next">下一页</a>
        {{end}}
    </div>
    {{end}}
</div>
{{else}}
<div class="no-content">
    <p>没有找到相关视频，请尝试其他关键词</p>
</div>
{{end}}

{{else}}
<div class="search-form-section">
    <h1>视频搜索</h1>
    <div class="search-form">
        <form action="/search" method="get">
            <input type="text" name="keyword" placeholder="请输入要搜索的视频名称..." value="">
            <button type="submit">搜索</button>
        </form>
    </div>
</div>
{{end}}
{{end}}
