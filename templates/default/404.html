{{template "layout.html" .}}

{{define "title"}}页面未找到 - 视频网站{{end}}

{{define "content"}}
<div class="error-page">
    <div class="error-content">
        <div class="error-code">404</div>
        <h1>页面未找到</h1>
        <p class="error-message">
            {{if .message}}
            {{.message}}
            {{else}}
            抱歉，您访问的页面不存在或已被删除。
            {{end}}
        </p>
        
        <div class="error-actions">
            <a href="/" class="btn btn-primary">返回首页</a>
            <a href="javascript:history.back()" class="btn btn-secondary">返回上页</a>
        </div>
        
        <div class="search-section">
            <h3>或者尝试搜索您需要的内容:</h3>
            <form action="/search" method="get" class="search-form">
                <input type="text" name="keyword" placeholder="搜索视频..." required>
                <button type="submit">搜索</button>
            </form>
        </div>
    </div>
</div>
{{end}}

{{define "head"}}
<style>
.error-page {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

.error-content {
    text-align: center;
    max-width: 500px;
    background: #fff;
    padding: 60px 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.error-code {
    font-size: 120px;
    font-weight: bold;
    color: #3498db;
    line-height: 1;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-content h1 {
    color: #2c3e50;
    font-size: 32px;
    margin-bottom: 15px;
}

.error-message {
    color: #666;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 40px;
}

.error-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 40px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s;
    border: 2px solid transparent;
}

.btn-primary {
    background: #3498db;
    color: #fff;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #666;
    border-color: #ddd;
}

.btn-secondary:hover {
    background: #f8f9fa;
    border-color: #bbb;
    color: #333;
}

.search-section {
    border-top: 1px solid #eee;
    padding-top: 30px;
}

.search-section h3 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 20px;
}

.search-form {
    display: flex;
    gap: 10px;
    max-width: 350px;
    margin: 0 auto;
}

.search-form input {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.search-form input:focus {
    outline: none;
    border-color: #3498db;
}

.search-form button {
    padding: 10px 20px;
    background: #3498db;
    color: #fff;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s;
}

.search-form button:hover {
    background: #2980b9;
}

@media (max-width: 768px) {
    .error-content {
        padding: 40px 30px;
        margin: 0 10px;
    }
    
    .error-code {
        font-size: 80px;
    }
    
    .error-content h1 {
        font-size: 24px;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 200px;
    }
    
    .search-form {
        flex-direction: column;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    .error-content {
        padding: 30px 20px;
    }
    
    .error-code {
        font-size: 60px;
    }
    
    .error-content h1 {
        font-size: 20px;
    }
    
    .error-message {
        font-size: 14px;
    }
}

/* 添加一些动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-content {
    animation: fadeInUp 0.6s ease-out;
}

.error-code {
    animation: fadeInUp 0.8s ease-out;
}
</style>
{{end}}
