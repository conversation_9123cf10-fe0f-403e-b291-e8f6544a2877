<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .page_title }} - 视频网站</title>
    <link rel="stylesheet" href="/default/css/style.css">
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="/">视频网站</a>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="/">首页</a></li>
                </ul>
            </nav>
            <div class="search">
                <form action="/search" method="get">
                    <input type="text" name="keyword" placeholder="搜索视频..." value="">
                    <button type="submit">搜索</button>
                </form>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main">
        <div class="container">
            <div class="error-page">
                <h1>404</h1>
                <h2>页面未找到</h2>
                <p>{{ if .message }}{{ .message }}{{ else }}抱歉，您访问的页面不存在。{{ end }}</p>
                <div class="error-actions">
                    <a href="/" class="btn">返回首页</a>
                    <a href="javascript:history.back()" class="btn btn-secondary">返回上页</a>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 视频网站. All rights reserved.</p>
        </div>
    </footer>

    <script src="/default/js/main.js"></script>
</body>
</html>
