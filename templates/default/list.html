{{template "layout.html" .}}

{{define "title"}}{{.page_title}} - 视频网站{{end}}

{{define "breadcrumb"}}
<div class="breadcrumb">
    <a href="/">首页</a> > <span>{{.type.TypeName}}</span>
</div>
{{end}}

{{define "content"}}
<h1>{{.type.TypeName}}</h1>

{{if .videos}}
<div class="video-grid">
    {{range .videos}}
    <div class="video-item">
        <div class="video-thumb">
            <a href="/detail/{{.VodID}}">
                {{if .VodPic}}
                <img src="{{fixImagePath .VodPic}}" alt="{{.VodName}}" loading="lazy">
                {{else}}
                <img src="/default/images/no-image.jpg" alt="{{.VodName}}" loading="lazy">
                {{end}}
            </a>
        </div>
        <div class="video-info">
            <h3><a href="/detail/{{.VodID}}">{{.VodName}}</a></h3>
            <p class="video-meta">
                {{if .VodRemarks}}<span class="remarks">{{.VodRemarks}}</span>{{end}}
                {{if .VodYear}}<span class="year">{{.VodYear}}</span>{{end}}
            </p>
        </div>
    </div>
    {{end}}
</div>

<!-- 分页 -->
{{if gt .pagination.totalPages 1}}
<div class="pagination">
    {{if gt .pagination.current 1}}
    <a href="/type/{{.type.TypeID}}?page={{subtract .pagination.current 1}}" class="prev">上一页</a>
    {{end}}
    
    {{$current := .pagination.current}}
    {{$total := .pagination.totalPages}}
    {{$typeID := .type.TypeID}}
    
    {{range $i := 1}}
    {{if le $i $total}}
        {{if eq $i $current}}
        <span class="current">{{$i}}</span>
        {{else}}
        <a href="/type/{{$typeID}}?page={{$i}}">{{$i}}</a>
        {{end}}
    {{end}}
    {{end}}
    
    {{if lt .pagination.current .pagination.totalPages}}
    <a href="/type/{{.type.TypeID}}?page={{add .pagination.current 1}}" class="next">下一页</a>
    {{end}}
</div>
{{end}}

{{else}}
<div class="no-content">
    <p>该栏目暂无视频内容</p>
</div>
{{end}}
{{end}}
