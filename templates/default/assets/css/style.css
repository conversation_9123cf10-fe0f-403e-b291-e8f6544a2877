/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
}

.logo a {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    text-decoration: none;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav a {
    color: #666;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s;
}

.nav a:hover,
.nav a.active {
    background: #3498db;
    color: #fff;
}

.search form {
    display: flex;
    gap: 10px;
}

.search input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 200px;
}

.search button {
    padding: 8px 16px;
    background: #3498db;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.search button:hover {
    background: #2980b9;
}

/* 主要内容 */
.main {
    min-height: calc(100vh - 140px);
    padding: 30px 0;
}

.breadcrumb {
    margin-bottom: 20px;
    color: #666;
}

.breadcrumb a {
    color: #3498db;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* 栏目分区 */
.type-section {
    margin-bottom: 50px;
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 15px;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 24px;
    margin: 0;
}

.more-link {
    color: #3498db;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s;
}

.more-link:hover {
    color: #2980b9;
}

.section-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.video-list h3 {
    color: #34495e;
    font-size: 18px;
    margin-bottom: 20px;
    padding-left: 15px;
    border-left: 4px solid #3498db;
}

/* 视频网格 */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.video-item {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.video-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.video-thumb {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 比例 */
    overflow: hidden;
}

.video-thumb img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.video-item:hover .video-thumb img {
    transform: scale(1.05);
}

.video-info {
    padding: 15px;
}

.video-info h3,
.video-info h4 {
    margin-bottom: 8px;
}

.video-info h3 a,
.video-info h4 a {
    color: #333;
    text-decoration: none;
    font-size: 16px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-info h3 a:hover,
.video-info h4 a:hover {
    color: #3498db;
}

.video-meta {
    color: #666;
    font-size: 14px;
}

.video-meta span {
    margin-right: 10px;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination a,
.pagination span {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s;
}

.pagination a:hover {
    background: #3498db;
    color: #fff;
    border-color: #3498db;
}

.pagination .current {
    background: #3498db;
    color: #fff;
    border-color: #3498db;
}

/* 无内容提示 */
.no-content {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

/* 底部 */
.footer {
    background: #2c3e50;
    color: #fff;
    text-align: center;
    padding: 20px 0;
    margin-top: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 15px;
    }
    
    .nav ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }
    
    .search input {
        width: 150px;
    }
    
    .video-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }
    
    .section-content {
        gap: 30px;
    }
    
    .type-section {
        padding: 20px;
        margin-bottom: 30px;
    }
}
