{{template "layout.html" .}}

{{define "title"}}{{.page_title}} - 视频网站{{end}}

{{define "breadcrumb"}}
<div class="breadcrumb">
    <a href="/">首页</a> > 
    <a href="/type/{{.type.TypeID}}">{{.type.TypeName}}</a> > 
    <span>{{.vod.VodName}}</span>
</div>
{{end}}

{{define "content"}}
<div class="video-detail">
    <div class="video-info-section">
        <div class="video-poster">
            {{if .vod.VodPic}}
            <img src="{{fixImagePath .vod.VodPic}}" alt="{{.vod.VodName}}">
            {{else}}
            <img src="/assets/images/no-image.jpg" alt="{{.vod.VodName}}">
            {{end}}
        </div>
        <div class="video-details">
            <h1>{{.vod.VodName}}</h1>
            <div class="meta-info">
                {{if .vod.VodSub}}
                <p><strong>别名：</strong>{{.vod.VodSub}}</p>
                {{end}}
                {{if .vod.VodActor}}
                <p><strong>主演：</strong>{{.vod.VodActor}}</p>
                {{end}}
                {{if .vod.VodDirector}}
                <p><strong>导演：</strong>{{.vod.VodDirector}}</p>
                {{end}}
                {{if .vod.VodArea}}
                <p><strong>地区：</strong>{{.vod.VodArea}}</p>
                {{end}}
                {{if .vod.VodYear}}
                <p><strong>年份：</strong>{{.vod.VodYear}}</p>
                {{end}}
                {{if .vod.VodLang}}
                <p><strong>语言：</strong>{{.vod.VodLang}}</p>
                {{end}}
                {{if .vod.VodRemarks}}
                <p><strong>状态：</strong>{{.vod.VodRemarks}}</p>
                {{end}}
            </div>
        </div>
    </div>

    {{if .vod.VodContent}}
    <div class="video-description">
        <h3>剧情简介</h3>
        <p>{{.vod.VodContent}}</p>
    </div>
    {{end}}

    {{if .episodes}}
    <div class="video-episodes">
        <h3>播放列表</h3>
        <div class="episodes-grid">
            {{range .episodes}}
            <div class="episode-item">
                <a href="{{.EpisodeUrl}}" target="_blank">
                    {{if .EpisodeTitle}}
                    {{.EpisodeTitle}}
                    {{else}}
                    第{{.EpisodeNum}}集
                    {{end}}
                </a>
            </div>
            {{end}}
        </div>
    </div>
    {{end}}
</div>
{{end}}
