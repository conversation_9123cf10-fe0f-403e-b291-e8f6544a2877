package routes

import (
	"html/template"
	"strings"

	"awesomeProject7/controllers"
	"awesomeProject7/middleware"
	"awesomeProject7/vod"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
)

// SetupRouter 设置路由
func SetupRouter() *gin.Engine {
	// 创建默认的gin路由
	router := gin.Default()

	// 添加模板函数
	router.SetFuncMap(template.FuncMap{
		"add": func(a, b int) int {
			return a + b
		},
		"subtract": func(a, b int) int {
			return a - b
		},
		"mul": func(a, b int) int {
			return a * b
		},
		// 修复图片路径，将反斜杠替换为正斜杠
		"fixImagePath": func(path string) string {
			if path == "" {
				return ""
			}
			// 将反斜杠替换为正斜杠
			path = strings.ReplaceAll(path, "\\", "/")
			// 确保路径以/开头
			if !strings.HasPrefix(path, "/") {
				path = "/" + path
			}
			return path
		},
	})

	// 设置会话存储
	store := cookie.NewStore([]byte("secret"))
	router.Use(sessions.Sessions("admin_session", store))

	// 加载模板 - 明确指定模板文件避免冲突
	router.LoadHTMLFiles(
		// 管理后台模板
		"templates/admin/index.html",
		"templates/admin/login.html",
		"templates/admin/collector.html",
		"templates/admin/category.html",
		"templates/admin/category_videos.html",
		"templates/admin/video_detail.html",
		// 安装模板
		"templates/install/step1.html",
		"templates/install/step2.html",
		"templates/install/step3.html",
		"templates/install/complete.html",
		// 前端模板
		"templates/default/layout.html",
		"templates/default/index.html",
		"templates/default/list.html",
		"templates/default/detail.html",
		"templates/default/search.html",
		"templates/default/404.html",
	)

	// 静态文件
	router.Static("/static", "./public")
	// 图片路径
	router.Static("/images", "./public/images")
	// 前端模板静态资源
	router.Static("/assets", "./templates/default/assets")

	// 创建控制器实例
	adminController := &controllers.AdminController{}
	installController := &controllers.InstallController{}
	collectorController := &controllers.CollectorController{}
	frontendController := &vod.FrontendController{}

	// 安装检查中间件
	router.Use(installController.CheckInstall)

	// 前端路由
	router.GET("/", frontendController.ShowIndex)
	router.GET("/type/:id", frontendController.ShowTypeList)
	router.GET("/detail/:id", frontendController.ShowVideoDetail)
	router.GET("/search", frontendController.ShowSearch)

	// 安装相关路由
	router.GET("/install", installController.ShowInstallStep1)
	router.GET("/install/step1", installController.ShowInstallStep1)
	router.GET("/install/step2", installController.ShowInstallStep2)
	router.POST("/install/step2", installController.ProcessInstallStep2)
	router.GET("/install/step3", installController.ShowInstallStep3)
	router.POST("/install/step3", installController.ProcessInstallStep3)
	router.GET("/install/complete", installController.ShowInstallComplete)

	// 管理后台路由组
	adminGroup := router.Group("/admin")
	{
		// 登录相关路由
		adminGroup.GET("/login", adminController.ShowLoginPage)
		adminGroup.POST("/login", adminController.Login)
		adminGroup.GET("/logout", middleware.AdminRequired(), adminController.Logout)

		// 需要管理员权限的路由
		authorized := adminGroup.Group("/")
		authorized.Use(middleware.AdminRequired())
		{
			authorized.GET("/", adminController.ShowDashboard) // 后台首页

			// 栏目相关路由
			authorized.GET("/category", adminController.ShowCategory)
			authorized.GET("/category/:id", adminController.ShowCategoryVideos)
			authorized.GET("/video/:id", adminController.ShowVideoDetail)
			authorized.POST("/category/update/:id", adminController.UpdateCategory)

			// 采集相关路由
			collector := authorized.Group("/collector")
			{
				collector.GET("", collectorController.ShowCollector)
				collector.POST("/start", collectorController.StartCollect)
				collector.POST("/stop", collectorController.StopCollect)
				collector.GET("/status", collectorController.GetStatus)
				collector.GET("/logs", collectorController.GetLogs)
				collector.POST("/clear-logs", collectorController.ClearLogs)
			}
		}
	}

	return router
}
