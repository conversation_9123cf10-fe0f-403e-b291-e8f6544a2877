package vod

import (
	"fmt"
	"net/http"
	"strconv"

	"awesomeProject7/models"

	"github.com/gin-gonic/gin"
)

// FrontendController 前端控制器
type FrontendController struct{}

// ShowIndex 显示首页
func (fc *FrontendController) ShowIndex(c *gin.Context) {
	// 获取所有父栏目（pid=0的栏目）
	parentTypes, err := models.FindTypesByPid(models.DB, 0)
	if err != nil {
		parentTypes = []*models.Type{}
	}

	// 为每个父栏目获取视频数据
	var typeSections []map[string]interface{}

	for _, parentType := range parentTypes {
		// 获取热门视频（按点击量排序）
		hotVideos, _, _ := models.FindVodsByTypeIDOrderBy(models.DB, parentType.TypeID, 1, 10, "vod_hits DESC")

		// 获取最新视频（按时间排序）
		latestVideos, _, _ := models.FindVodsByTypeIDOrderBy(models.DB, parentType.TypeID, 1, 10, "vod_time DESC")

		// 获取随机视频
		randomVideos, _, _ := models.FindRandomVodsByTypeID(models.DB, parentType.TypeID, 10)

		typeSection := map[string]interface{}{
			"TypeID":        parentType.TypeID,
			"TypeName":      parentType.TypeName,
			"hot_videos":    hotVideos,
			"latest_videos": latestVideos,
			"random_videos": randomVideos,
		}

		typeSections = append(typeSections, typeSection)
	}

	// 调试信息
	fmt.Printf("父栏目数量: %d\n", len(parentTypes))
	fmt.Printf("栏目分区数量: %d\n", len(typeSections))
	for i, section := range typeSections {
		fmt.Printf("分区 %d: %s, 热门视频: %d, 最新视频: %d, 随机视频: %d\n",
			i, section["TypeName"],
			len(section["hot_videos"].([]*models.Vod)),
			len(section["latest_videos"].([]*models.Vod)),
			len(section["random_videos"].([]*models.Vod)))
	}

	c.HTML(http.StatusOK, "index.html", gin.H{
		"page_title":     "首页",
		"current_page":   "index",
		"parent_types":   parentTypes,
		"type_sections":  typeSections,
	})
}

// ShowTypeList 显示栏目列表页
func (fc *FrontendController) ShowTypeList(c *gin.Context) {
	// 获取栏目ID
	typeIDStr := c.Param("id")
	typeID, err := strconv.Atoi(typeIDStr)
	if err != nil {
		c.HTML(http.StatusNotFound, "templates/default/404.html", gin.H{
			"page_title": "页面未找到",
			"message":    "栏目不存在",
		})
		return
	}

	// 获取页码
	pageStr := c.DefaultQuery("page", "1")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize := 20 // 每页显示20个视频

	// 获取栏目信息
	typeInfo, err := models.FindTypeByID(models.DB, typeID)
	if err != nil {
		c.HTML(http.StatusNotFound, "templates/default/404.html", gin.H{
			"page_title": "页面未找到",
			"message":    "栏目不存在",
		})
		return
	}

	// 获取栏目下的视频列表
	videos, total, err := models.FindVodsByTypeID(models.DB, typeID, page, pageSize)
	if err != nil {
		videos = []*models.Vod{}
		total = 0
	}

	// 计算总页数
	totalPages := (total + int64(pageSize) - 1) / int64(pageSize)

	// 获取所有父栏目（用于导航）
	parentTypes, err := models.FindTypesByPid(models.DB, 0)
	if err != nil {
		parentTypes = []*models.Type{}
	}

	c.HTML(http.StatusOK, "templates/default/list.html", gin.H{
		"page_title":      typeInfo.TypeName,
		"current_type_id": typeID,
		"type":            typeInfo,
		"videos":          videos,
		"parent_types":    parentTypes,
		"pagination": map[string]interface{}{
			"current":    page,
			"total":      total,
			"totalPages": totalPages,
			"pageSize":   pageSize,
		},
	})
}

// ShowVideoDetail 显示视频详情页
func (fc *FrontendController) ShowVideoDetail(c *gin.Context) {
	// 获取视频ID
	vodIDStr := c.Param("id")
	vodID, err := strconv.Atoi(vodIDStr)
	if err != nil {
		c.HTML(http.StatusNotFound, "templates/default/404.html", gin.H{
			"page_title": "页面未找到",
			"message":    "视频不存在",
		})
		return
	}

	// 获取视频信息
	vod, err := models.FindVodByID(models.DB, vodID)
	if err != nil {
		c.HTML(http.StatusNotFound, "templates/default/404.html", gin.H{
			"page_title": "页面未找到",
			"message":    "视频不存在",
		})
		return
	}

	// 获取栏目信息
	typeInfo, err := models.FindTypeByID(models.DB, vod.TypeID)
	if err != nil {
		typeInfo = &models.Type{TypeName: "未知栏目"}
	}

	// 获取视频剧集列表
	episodes, err := models.FindEpisodesByVodID(models.DB, vodID)
	if err != nil {
		episodes = []*models.VodEpisode{}
	}

	// 获取所有父栏目（用于导航）
	parentTypes, err := models.FindTypesByPid(models.DB, 0)
	if err != nil {
		parentTypes = []*models.Type{}
	}

	c.HTML(http.StatusOK, "templates/default/detail.html", gin.H{
		"page_title":   vod.VodName,
		"vod":          vod,
		"type":         typeInfo,
		"episodes":     episodes,
		"parent_types": parentTypes,
	})
}

// ShowSearch 显示搜索页面
func (fc *FrontendController) ShowSearch(c *gin.Context) {
	keyword := c.Query("keyword")
	pageStr := c.DefaultQuery("page", "1")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize := 20
	var videos []*models.Vod
	var total int64

	if keyword != "" {
		// 执行搜索
		videos, total, err = models.SearchVods(models.DB, keyword, page, pageSize)
		if err != nil {
			videos = []*models.Vod{}
			total = 0
		}
	} else {
		videos = []*models.Vod{}
		total = 0
	}

	// 计算总页数
	totalPages := (total + int64(pageSize) - 1) / int64(pageSize)

	// 获取所有父栏目（用于导航）
	parentTypes, err := models.FindTypesByPid(models.DB, 0)
	if err != nil {
		parentTypes = []*models.Type{}
	}

	c.HTML(http.StatusOK, "templates/default/search.html", gin.H{
		"page_title":   "搜索结果",
		"keyword":      keyword,
		"videos":       videos,
		"parent_types": parentTypes,
		"pagination": map[string]interface{}{
			"current":    page,
			"total":      total,
			"totalPages": totalPages,
			"pageSize":   pageSize,
		},
	})
}
